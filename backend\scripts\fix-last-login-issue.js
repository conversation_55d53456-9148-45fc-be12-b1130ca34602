#!/usr/bin/env node

/**
 * Fix Last Login Issue
 * This script fixes the validate_enum_value error that occurs during last_login updates
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testLastLoginUpdate() {
  console.log('🧪 Testing last_login update to reproduce error...');
  
  try {
    // Get a test profile
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .limit(1);

    if (profileError || !profiles || profiles.length === 0) {
      console.log('⚠️ No profiles found for testing');
      return false;
    }

    const testProfile = profiles[0];
    console.log(`📝 Testing with profile: ${testProfile.email}`);

    // Try to update last_login (this should trigger the error)
    const { data, error } = await supabase
      .from('profiles')
      .update({ 
        last_login: new Date().toISOString()
      })
      .eq('id', testProfile.id);

    if (error) {
      console.error('❌ Last login update failed:', error.message);
      if (error.message.includes('validate_enum_value')) {
        console.log('🎯 CONFIRMED! last_login update triggers validate_enum_value error');
        return true;
      }
    } else {
      console.log('✅ Last login update successful');
      return false;
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    if (error.message && error.message.includes('validate_enum_value')) {
      console.log('🎯 CONFIRMED! last_login update triggers validate_enum_value error');
      return true;
    }
    return false;
  }
}

async function createFixForLastLoginIssue() {
  console.log('🔧 Creating fix for last_login issue...');
  
  console.log('\n📋 SQL to run in Supabase Dashboard:');
  console.log('='.repeat(60));
  
  const sql = `
-- =====================================================
-- FIX LAST_LOGIN UPDATE ISSUE
-- =====================================================

-- 1. Create all missing validate_enum_value function signatures
-- This ensures the function works with any data type

-- Main function (text, text) - should already exist
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check text, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Validate based on enum type
  CASE enum_type
    WHEN 'user_role' THEN
      RETURN value_to_check IN ('patient', 'doctor', 'admin');
    
    WHEN 'gender' THEN
      RETURN value_to_check IN ('male', 'female', 'other');
    
    WHEN 'blood_type' THEN
      RETURN value_to_check IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-');
    
    WHEN 'appointment_status' THEN
      RETURN value_to_check IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
    
    WHEN 'qualification' THEN
      RETURN value_to_check IN ('Bác sĩ', 'Thạc sĩ', 'Tiến sĩ', 'Giáo sư', 'Phó Giáo sư');
    
    WHEN 'specialization' THEN
      RETURN value_to_check IN (
        'Nội tổng hợp', 'Ngoại tổng hợp', 'Sản phụ khoa', 'Nhi khoa',
        'Tim mạch can thiệp', 'Thần kinh học', 'Chấn thương và chỉnh hình',
        'Cấp cứu và hồi sức', 'Da liễu', 'Mắt', 'Tai mũi họng', 'Răng hàm mặt'
      );
    
    WHEN 'user_status' THEN
      RETURN value_to_check IN ('active', 'inactive', 'pending', 'suspended');
    
    ELSE
      -- For unknown enum types, return true (permissive)
      RETURN true;
  END CASE;
END;
$$;

-- Function for unknown type (the problematic one)
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check unknown, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Handle unknown type by converting to text
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Convert to text and use main validation
  RETURN validate_enum_value(value_to_check::text, enum_type);
EXCEPTION
  WHEN OTHERS THEN
    -- If conversion fails, return true (permissive)
    RETURN true;
END;
$$;

-- Function for anyelement type
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check anyelement, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Handle anyelement type by converting to text
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Convert to text and use main validation
  RETURN validate_enum_value(value_to_check::text, enum_type);
EXCEPTION
  WHEN OTHERS THEN
    -- If conversion fails, return true (permissive)
    RETURN true;
END;
$$;

-- Function for timestamp type (for last_login field)
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check timestamp with time zone, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Timestamps are always valid, no enum validation needed
  RETURN true;
END;
$$;

-- Function for timestamp without time zone
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check timestamp without time zone, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Timestamps are always valid, no enum validation needed
  RETURN true;
END;
$$;

-- Nullable helper function
CREATE OR REPLACE FUNCTION validate_enum_value_nullable(value_to_check text, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Allow NULL values
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Use main validation function
  RETURN validate_enum_value(value_to_check, enum_type);
END;
$$;

-- 2. Grant permissions to all function signatures
GRANT EXECUTE ON FUNCTION validate_enum_value(text, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_enum_value(unknown, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_enum_value(anyelement, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_enum_value(timestamp with time zone, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_enum_value(timestamp without time zone, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_enum_value_nullable(text, text) TO authenticated, anon, service_role;

-- 3. Test the functions
SELECT 
  'Testing all function signatures...' as test_start,
  validate_enum_value('patient'::text, 'user_role') as test_text,
  validate_enum_value(NOW()::timestamp, 'user_role') as test_timestamp,
  validate_enum_value_nullable(NULL, 'gender') as test_nullable;

-- 4. Check if there are any problematic constraints
-- (This will show any constraints that might be causing issues)
SELECT 
  table_name,
  constraint_name,
  constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'profiles' 
  AND constraint_type = 'CHECK';

SELECT 'Fix for last_login issue completed!' as status;
`;

  console.log(sql);
  console.log('='.repeat(60));
  console.log('\nThis should fix the validate_enum_value error during last_login updates.');
}

async function testAfterFix() {
  console.log('🧪 Testing after fix (simulation)...');
  
  try {
    // Test the main function that should work
    const { data, error } = await supabase.rpc('validate_enum_value', {
      value_to_check: 'patient',
      enum_type: 'user_role'
    });

    if (error) {
      console.log('❌ Function still has issues:', error.message);
    } else {
      console.log('✅ Main function working:', data);
    }

    // Test nullable function
    const { data: nullData, error: nullError } = await supabase.rpc('validate_enum_value_nullable', {
      value_to_check: null,
      enum_type: 'user_role'
    });

    if (nullError) {
      console.log('❌ Nullable function has issues:', nullError.message);
    } else {
      console.log('✅ Nullable function working:', nullData);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function suggestFrontendFix() {
  console.log('🔧 Suggesting frontend fix as alternative...');
  
  console.log('\n📋 Alternative: Update frontend code to handle the error gracefully');
  console.log('='.repeat(60));
  
  const frontendFix = `
// In frontend/lib/auth/supabase-auth.ts, replace the last_login update:

// OLD CODE (line 327-330):
await supabaseClient
  .from('profiles')
  .update({ last_login: new Date().toISOString() })
  .eq('id', authData.user.id);

// NEW CODE (with error handling):
try {
  await supabaseClient
    .from('profiles')
    .update({ last_login: new Date().toISOString() })
    .eq('id', authData.user.id);
} catch (error) {
  // Log the error but don't fail the login
  console.warn('⚠️ Could not update last_login:', error);
  // Login still succeeds even if last_login update fails
}
`;

  console.log(frontendFix);
  console.log('='.repeat(60));
  console.log('\nThis will prevent the login from failing even if last_login update has issues.');
}

async function main() {
  console.log('🚀 Starting fix for last_login validate_enum_value issue...');
  
  try {
    // 1. Test to confirm the issue
    const hasIssue = await testLastLoginUpdate();
    
    // 2. Provide database fix
    await createFixForLastLoginIssue();
    
    // 3. Test current functions
    await testAfterFix();
    
    // 4. Suggest frontend alternative
    await suggestFrontendFix();
    
    console.log('\n📊 Summary:');
    console.log(`Issue confirmed: ${hasIssue ? '✅ YES' : '❌ NO'}`);
    console.log('Database fix: ✅ PROVIDED (run SQL above)');
    console.log('Frontend fix: ✅ PROVIDED (alternative solution)');
    
    console.log('\n🎯 Recommended action:');
    if (hasIssue) {
      console.log('1. Run the SQL fix in Supabase Dashboard (preferred)');
      console.log('2. OR apply the frontend fix as a workaround');
    } else {
      console.log('The issue might be intermittent. Apply the SQL fix as prevention.');
    }

  } catch (error) {
    console.error('❌ Fix process failed:', error);
  }
}

main().catch(console.error);
