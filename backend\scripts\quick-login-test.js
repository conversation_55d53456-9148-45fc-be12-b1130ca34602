const { createClient } = require('@supabase/supabase-js');
const path = require('path');

require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function quickLoginTest() {
  console.log('⚡ QUICK LOGIN TEST');
  console.log('='.repeat(30));
  
  const testEmail = `quick-test-${Date.now()}@example.com`;
  const testPassword = 'QuickTest123!';
  
  try {
    // 1. Quick signup
    console.log('📝 Quick signup...');
    const { data: signupData, error: signupError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword
    });
    
    if (signupError) {
      console.error('❌ Signup failed:', signupError.message);
      return false;
    }
    
    console.log('✅ Signup OK');
    
    // 2. Wait briefly
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. Quick login
    console.log('🔐 Quick login...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });
    
    if (loginError) {
      console.error('❌ Login failed:', loginError.message);
      console.error('📋 Error code:', loginError.status);
      return false;
    }
    
    console.log('✅ LOGIN SUCCESS!');
    console.log('👤 User ID:', loginData.user?.id);
    console.log('📧 Email:', loginData.user?.email);
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

quickLoginTest().then(success => {
  if (success) {
    console.log('\n🎉 🎉 🎉 LOGIN WORKS! 🎉 🎉 🎉');
    console.log('✅ The function fix was successful!');
    console.log('✅ You can now use your app normally!');
  } else {
    console.log('\n❌ Login still broken');
    console.log('🔧 Try the EMERGENCY FIX script');
  }
}).catch(console.error);
