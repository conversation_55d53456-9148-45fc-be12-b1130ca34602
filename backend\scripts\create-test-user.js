const { createClient } = require('@supabase/supabase-js');
const path = require('path');

require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Use service role key

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestUser() {
  console.log('🔧 Creating test user with service role...');
  
  const testEmail = `test-user-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  
  try {
    // 1. Create auth user with service role
    console.log('📧 Creating auth user:', testEmail);
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        full_name: 'Test User',
        role: 'patient'
      }
    });
    
    if (authError) {
      console.error('❌ Auth user creation failed:', authError);
      return;
    }
    
    console.log('✅ Auth user created:', authData.user.id);
    
    // 2. Create profile directly
    console.log('👤 Creating profile...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email: testEmail,
        full_name: 'Test User',
        role: 'patient',
        phone_number: '**********',
        is_active: true,
        email_verified: true
      })
      .select()
      .single();
    
    if (profileError) {
      console.error('❌ Profile creation failed:', profileError);
      return;
    }
    
    console.log('✅ Profile created:', profileData);
    
    // 3. Test login immediately
    console.log('🔐 Testing login...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });
    
    if (loginError) {
      console.error('❌ Login test failed:', loginError);
      console.log('📋 Login error details:', {
        message: loginError.message,
        status: loginError.status,
        code: loginError.code
      });
      return;
    }
    
    console.log('✅ Login test successful!');
    console.log('👤 Logged in user:', loginData.user.email);
    
    // 4. Test profile access
    console.log('📋 Testing profile access...');
    
    const { data: profileAccess, error: accessError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', loginData.user.id)
      .single();
    
    if (accessError) {
      console.error('❌ Profile access failed:', accessError);
      return;
    }
    
    console.log('✅ Profile access successful!');
    console.log('📋 Profile data:', profileAccess);
    
    console.log('\n🎉 SUCCESS! Test user created and login works!');
    console.log('📧 Test credentials:');
    console.log(`Email: ${testEmail}`);
    console.log(`Password: ${testPassword}`);
    console.log('\n💡 Try logging in with these credentials in your app!');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

createTestUser();
