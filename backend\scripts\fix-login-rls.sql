-- =====================================================
-- FIX LOGIN RLS POLICIES
-- =====================================================
-- This script fixes the "Database error granting user" login issue

-- 1. Temporarily disable <PERSON><PERSON> to fix policies
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. Drop all existing policies that might be causing conflicts
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Allow profile creation during signup" ON profiles;
DROP POLICY IF EXISTS "allow_insert_for_authenticated" ON profiles;
DROP POLICY IF EXISTS "allow_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "allow_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "allow_all_for_service_role" ON profiles;
DROP POLICY IF EXISTS "allow_insert_during_signup" ON profiles;
DROP POLICY IF EXISTS "allow_select_own" ON profiles;
DROP POLICY IF EXISTS "allow_update_own" ON profiles;

-- 3. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon, service_role;
GRANT ALL ON public.profiles TO authenticated, anon, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated, anon, service_role;

-- 4. Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 5. Create simple, working policies for login
CREATE POLICY "allow_insert_for_signup" ON profiles
  FOR INSERT TO anon, authenticated 
  WITH CHECK (true);

CREATE POLICY "allow_select_for_users" ON profiles
  FOR SELECT TO authenticated 
  USING (auth.uid() = id);

CREATE POLICY "allow_update_for_users" ON profiles
  FOR UPDATE TO authenticated 
  USING (auth.uid() = id) 
  WITH CHECK (auth.uid() = id);

CREATE POLICY "allow_all_for_service" ON profiles
  FOR ALL TO service_role 
  USING (true) 
  WITH CHECK (true);

-- 6. Test profile access
DO $$
BEGIN
  -- Test if we can query profiles
  PERFORM * FROM profiles LIMIT 1;
  RAISE NOTICE 'SUCCESS: Profile table accessible';
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'ERROR: Profile table access failed: %', SQLERRM;
END;
$$;

-- 7. Verify policies
SELECT 'CURRENT POLICIES ON PROFILES:' as info;
SELECT 
  policyname,
  permissive,
  roles,
  cmd
FROM pg_policies 
WHERE tablename = 'profiles';

-- 8. Grant additional permissions for auth
GRANT SELECT ON auth.users TO authenticated;

SELECT 'Login RLS fix completed!' as final_status;
