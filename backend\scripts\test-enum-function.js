#!/usr/bin/env node

/**
 * Test Enum Validation Function
 * This script tests if the validate_enum_value function is working correctly
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testEnumFunction() {
  console.log('🧪 Testing enum validation function...');
  
  const tests = [
    { value: 'patient', type: 'user_role', expected: true },
    { value: 'invalid_role', type: 'user_role', expected: false },
    { value: 'male', type: 'gender', expected: true },
    { value: 'invalid_gender', type: 'gender', expected: false },
    { value: 'A+', type: 'blood_type', expected: true },
    { value: 'invalid_blood', type: 'blood_type', expected: false },
  ];

  let allPassed = true;

  for (const test of tests) {
    try {
      const { data, error } = await supabase.rpc('validate_enum_value', {
        value_to_check: test.value,
        enum_type: test.type
      });

      if (error) {
        console.error(`❌ Test failed for ${test.value}:`, error);
        allPassed = false;
        continue;
      }

      if (data === test.expected) {
        console.log(`✅ ${test.value} (${test.type}): ${data} ✓`);
      } else {
        console.log(`❌ ${test.value} (${test.type}): expected ${test.expected}, got ${data}`);
        allPassed = false;
      }
    } catch (err) {
      console.error(`❌ Exception testing ${test.value}:`, err);
      allPassed = false;
    }
  }

  // Test nullable function
  console.log('\n🧪 Testing nullable function...');
  try {
    const { data, error } = await supabase.rpc('validate_enum_value_nullable', {
      value_to_check: null,
      enum_type: 'gender'
    });

    if (error) {
      console.error('❌ Nullable test failed:', error);
      allPassed = false;
    } else if (data === true) {
      console.log('✅ NULL value test: true ✓');
    } else {
      console.log(`❌ NULL value test: expected true, got ${data}`);
      allPassed = false;
    }
  } catch (err) {
    console.error('❌ Nullable test exception:', err);
    allPassed = false;
  }

  return allPassed;
}

async function testProfileCreation() {
  console.log('\n👤 Testing profile creation...');
  
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';

  try {
    // 1. Create auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true
    });

    if (authError) {
      console.error('❌ Auth user creation failed:', authError);
      return false;
    }

    console.log('✅ Auth user created:', authData.user.id);

    // 2. Try to create profile
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email: testEmail,
        full_name: 'Test User',
        role: 'patient',
        phone_number: '**********',
        is_active: true,
        email_verified: true
      })
      .select()
      .single();

    if (profileError) {
      console.error('❌ Profile creation failed:', profileError);
      
      // Clean up auth user
      await supabase.auth.admin.deleteUser(authData.user.id);
      return false;
    }

    console.log('✅ Profile created successfully!');

    // 3. Clean up
    await supabase.from('profiles').delete().eq('id', authData.user.id);
    await supabase.auth.admin.deleteUser(authData.user.id);
    
    console.log('✅ Test data cleaned up');
    return true;

  } catch (error) {
    console.error('❌ Profile creation test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting comprehensive tests...');
  
  const enumTestPassed = await testEnumFunction();
  const profileTestPassed = await testProfileCreation();
  
  console.log('\n📊 Test Results:');
  console.log(`Enum Function: ${enumTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Profile Creation: ${profileTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (enumTestPassed && profileTestPassed) {
    console.log('\n🎉 ALL TESTS PASSED! Your system should be working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the errors above.');
  }
}

main().catch(console.error);
