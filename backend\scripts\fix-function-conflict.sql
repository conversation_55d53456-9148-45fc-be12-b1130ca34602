-- =====================================================
-- FIX FUNCTION CONFLICT - SINGLE FUNCTION SOLUTION
-- =====================================================
-- This creates only ONE function to avoid conflicts

-- 1. Drop all existing validate_enum_value functions
DROP FUNCTION IF EXISTS validate_enum_value CASCADE;
DROP FUNCTION IF EXISTS validate_enum_value_nullable CASCADE;

-- 2. Create ONLY ONE validate_enum_value function using anyelement
-- This will handle ALL data types including text, unknown, timestamps, etc.
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check anyelement, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  text_value text;
BEGIN
  -- Convert any input type to text safely
  BEGIN
    -- Handle NULL case first
    IF value_to_check IS NULL THEN
      RETURN true;
    END IF;
    
    -- Convert to text
    text_value := value_to_check::text;
    
    -- Handle empty string
    IF text_value = '' THEN
      RETURN true;
    END IF;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- If conversion fails, return true (permissive)
      RETURN true;
  END;

  -- Validate based on enum type
  CASE enum_type
    WHEN 'user_role' THEN
      RETURN text_value IN ('patient', 'doctor', 'admin');
    
    WHEN 'gender' THEN
      RETURN text_value IN ('male', 'female', 'other');
    
    WHEN 'blood_type' THEN
      RETURN text_value IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-');
    
    WHEN 'appointment_status' THEN
      RETURN text_value IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
    
    WHEN 'qualification' THEN
      RETURN text_value IN ('Bác sĩ', 'Thạc sĩ', 'Tiến sĩ', 'Giáo sư', 'Phó Giáo sư');
    
    WHEN 'specialization' THEN
      RETURN text_value IN (
        'Nội tổng hợp', 'Ngoại tổng hợp', 'Sản phụ khoa', 'Nhi khoa',
        'Tim mạch can thiệp', 'Thần kinh học', 'Chấn thương và chỉnh hình',
        'Cấp cứu và hồi sức', 'Da liễu', 'Mắt', 'Tai mũi họng', 'Răng hàm mặt'
      );
    
    WHEN 'user_status' THEN
      RETURN text_value IN ('active', 'inactive', 'pending', 'suspended');
    
    ELSE
      -- For unknown enum types, return true (permissive)
      RETURN true;
  END CASE;
  
EXCEPTION
  WHEN OTHERS THEN
    -- If anything fails, return true (permissive)
    RETURN true;
END;
$$;

-- 3. Create a nullable helper function with different name to avoid conflicts
CREATE OR REPLACE FUNCTION validate_enum_nullable(value_to_check text, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Allow NULL values
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Call the main validation function
  RETURN validate_enum_value(value_to_check::anyelement, enum_type);
END;
$$;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION validate_enum_value(anyelement, text) TO authenticated, anon, service_role, postgres;
GRANT EXECUTE ON FUNCTION validate_enum_nullable(text, text) TO authenticated, anon, service_role, postgres;

-- 5. Remove any problematic constraints
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN 
        SELECT tc.table_name, tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
        WHERE tc.constraint_type = 'CHECK' 
        AND cc.check_clause LIKE '%validate_enum_value%'
    LOOP
        EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I', 
                      constraint_record.table_name, 
                      constraint_record.constraint_name);
        RAISE NOTICE 'Dropped constraint % from table %', 
                     constraint_record.constraint_name, 
                     constraint_record.table_name;
    END LOOP;
END $$;

-- 6. Test the single function with different data types
SELECT 
  'Testing single validate_enum_value function...' as test_start;

-- Test with text
SELECT 
  'text test' as test_name,
  validate_enum_value('patient', 'user_role') as result;

-- Test with varchar
SELECT 
  'varchar test' as test_name,
  validate_enum_value('doctor'::varchar, 'user_role') as result;

-- Test with timestamp (this was causing the original error)
SELECT 
  'timestamp test' as test_name,
  validate_enum_value(NOW(), 'user_role') as result;

-- Test with NULL
SELECT 
  'null test' as test_name,
  validate_enum_value(NULL::text, 'user_role') as result;

-- Test invalid enum
SELECT 
  'invalid enum test' as test_name,
  validate_enum_value('invalid_role', 'user_role') as result;

-- Test nullable helper
SELECT 
  'nullable helper test' as test_name,
  validate_enum_nullable(NULL, 'gender') as result;

-- 7. Verify only one function exists
SELECT 
  proname as function_name,
  pg_get_function_arguments(oid) as arguments
FROM pg_proc 
WHERE proname LIKE '%validate_enum%'
ORDER BY proname;

-- 8. Success message
SELECT 'Single validate_enum_value function created - no more conflicts!' as status;
