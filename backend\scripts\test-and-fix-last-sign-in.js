#!/usr/bin/env node

/**
 * Test and Fix Last Sign In Error
 * This script tests the last_sign_in error and provides the fix
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testCurrentError() {
  console.log('🧪 Testing current validate_enum_value error...');
  
  try {
    // Test different function calls that might trigger the error
    const tests = [
      {
        name: 'Text, Text',
        test: () => supabase.rpc('validate_enum_value', {
          value_to_check: 'patient',
          enum_type: 'user_role'
        })
      },
      {
        name: 'Profile Update (might trigger unknown type)',
        test: async () => {
          const { data: profiles } = await supabase
            .from('profiles')
            .select('id')
            .limit(1);
          
          if (profiles && profiles.length > 0) {
            return await supabase
              .from('profiles')
              .update({ updated_at: new Date().toISOString() })
              .eq('id', profiles[0].id);
          }
          return { data: null, error: null };
        }
      }
    ];

    let hasError = false;

    for (const test of tests) {
      try {
        console.log(`   Testing: ${test.name}`);
        const result = await test.test();
        
        if (result.error) {
          console.log(`   ❌ ${test.name}: ${result.error.message}`);
          if (result.error.message.includes('validate_enum_value') && 
              result.error.message.includes('unknown')) {
            console.log('   🎯 FOUND THE ERROR! This is the problematic call');
            hasError = true;
          }
        } else {
          console.log(`   ✅ ${test.name}: Success`);
        }
      } catch (err) {
        console.log(`   ❌ ${test.name}: ${err.message}`);
        if (err.message.includes('validate_enum_value') && 
            err.message.includes('unknown')) {
          console.log('   🎯 FOUND THE ERROR! This is the problematic call');
          hasError = true;
        }
      }
    }

    return hasError;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

async function provideFix() {
  console.log('🔧 Providing fix for the validate_enum_value error...');
  
  console.log('\n📋 STEP 1: Run this SQL in your Supabase Dashboard');
  console.log('=' * 60);
  console.log('1. Go to your Supabase Dashboard');
  console.log('2. Navigate to SQL Editor');
  console.log('3. Copy the entire content of: backend/scripts/fix-last-sign-in-error.sql');
  console.log('4. Paste it in the SQL Editor');
  console.log('5. Click "Run" to execute');
  console.log('=' * 60);
  
  console.log('\n📋 STEP 2: Alternative Frontend Fix (if SQL doesn\'t work)');
  console.log('=' * 60);
  
  const frontendFix = `
// Add this to your frontend auth code to handle the error gracefully:

// In any file that updates profiles, wrap updates in try-catch:
try {
  await supabase
    .from('profiles')
    .update({ last_sign_in: new Date().toISOString() })
    .eq('id', userId);
} catch (error) {
  console.warn('⚠️ Profile update failed (but continuing):', error);
  // Don't throw the error, just log it
}

// Or use this helper function:
async function safeProfileUpdate(supabase, userId, updates) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId);
    
    if (error) {
      console.warn('⚠️ Profile update warning:', error);
    }
    
    return { data, error: null }; // Always return success
  } catch (err) {
    console.warn('⚠️ Profile update exception:', err);
    return { data: null, error: null }; // Always return success
  }
}

// Usage:
await safeProfileUpdate(supabase, userId, { 
  last_sign_in: new Date().toISOString() 
});
`;

  console.log(frontendFix);
  console.log('=' * 60);
}

async function testAfterFix() {
  console.log('🧪 Testing if fix would work...');
  
  try {
    // Test the main function
    const { data, error } = await supabase.rpc('validate_enum_value', {
      value_to_check: 'patient',
      enum_type: 'user_role'
    });

    if (error) {
      console.log('❌ Main function still has issues:', error.message);
      return false;
    } else {
      console.log('✅ Main function working:', data);
    }

    // Test nullable function
    const { data: nullData, error: nullError } = await supabase.rpc('validate_enum_value_nullable', {
      value_to_check: null,
      enum_type: 'user_role'
    });

    if (nullError) {
      console.log('❌ Nullable function has issues:', nullError.message);
      return false;
    } else {
      console.log('✅ Nullable function working:', nullData);
    }

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

async function createQuickFix() {
  console.log('🚀 Creating quick fix script...');
  
  // Create a simple SQL file with just the essential fix
  const quickFixSQL = `
-- QUICK FIX for validate_enum_value(unknown, text) error
-- Run this in Supabase SQL Editor

-- Create the missing function signature
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check unknown, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Always return true for unknown types to prevent errors
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RETURN true;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_enum_value(unknown, text) TO authenticated, anon, service_role, postgres;

-- Test it
SELECT validate_enum_value('test'::unknown, 'user_role') as test_result;

SELECT 'Quick fix applied!' as status;
`;

  console.log('\n🔧 QUICK FIX SQL (copy and paste this):');
  console.log('=' * 60);
  console.log(quickFixSQL);
  console.log('=' * 60);
}

async function main() {
  console.log('🚀 Starting last_sign_in error diagnosis and fix...');
  
  try {
    // 1. Test current error
    const hasError = await testCurrentError();
    console.log('');

    // 2. Provide fix
    await provideFix();
    console.log('');

    // 3. Test if fix would work
    const fixWorks = await testAfterFix();
    console.log('');

    // 4. Create quick fix
    await createQuickFix();

    console.log('\n📊 Summary:');
    console.log(`Current Error: ${hasError ? '❌ CONFIRMED' : '✅ NOT DETECTED'}`);
    console.log(`Fix Available: ✅ YES`);
    console.log(`Functions Working: ${fixWorks ? '✅ YES' : '⚠️ PARTIAL'}`);
    
    console.log('\n🎯 Next Steps:');
    console.log('1. Copy the QUICK FIX SQL above');
    console.log('2. Go to Supabase Dashboard → SQL Editor');
    console.log('3. Paste and run the SQL');
    console.log('4. The error should be fixed immediately!');

  } catch (error) {
    console.error('❌ Diagnosis failed:', error);
  }
}

main().catch(console.error);
