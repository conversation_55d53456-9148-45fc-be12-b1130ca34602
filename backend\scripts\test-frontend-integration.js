const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Simulate the frontend signup process
async function simulateFrontendSignup() {
  console.log('🎯 Simulating Frontend Signup Process');
  console.log('='.repeat(50));
  
  const testEmail = `frontend-test-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  const userData = {
    email: testEmail,
    password: testPassword,
    full_name: 'Frontend Test User',
    phone_number: '**********',
    role: 'patient'
  };
  
  try {
    console.log(`📧 Step 1: Creating auth user - ${testEmail}`);
    
    // Step 1: Create auth user (like frontend does)
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.full_name,
          phone_number: userData.phone_number,
          role: userData.role
        }
      }
    });
    
    if (authError) {
      console.error('❌ Auth signup failed:', authError);
      return false;
    }
    
    console.log('✅ Auth user created:', authData.user?.id);
    
    // Step 2: Simulate the createUserProfile method
    console.log('\n🏥 Step 2: Creating profile with multiple fallback methods...');
    
    const profileResult = await createUserProfileWithFallbacks(authData.user.id, userData);
    
    if (!profileResult.success) {
      console.error('❌ Profile creation failed:', profileResult.error);
      return false;
    }
    
    console.log('✅ Profile created successfully via:', profileResult.method);
    console.log('📋 Profile data:', profileResult.profile);
    
    // Step 3: Test login (like frontend would do after signup)
    console.log('\n🔐 Step 3: Testing login after signup...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });
    
    if (loginError) {
      console.error('❌ Login failed:', loginError);
      return false;
    }
    
    console.log('✅ Login successful!');
    
    // Step 4: Test profile access
    const { data: profileAccess, error: accessError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', loginData.user.id)
      .single();
    
    if (accessError) {
      console.error('❌ Profile access failed:', accessError);
      return false;
    }
    
    console.log('✅ Profile accessible after login!');
    console.log('📋 Accessible profile:', {
      email: profileAccess.email,
      full_name: profileAccess.full_name,
      role: profileAccess.role,
      is_active: profileAccess.is_active
    });
    
    await supabase.auth.signOut();
    return true;
    
  } catch (error) {
    console.error('❌ Frontend simulation failed:', error);
    return false;
  }
}

// Simulate the createUserProfile method from frontend
async function createUserProfileWithFallbacks(userId, userData) {
  console.log('🔄 Starting profile creation with fallbacks...');
  
  // Method 1: Wait for trigger
  console.log('🔄 Method 1: Checking if trigger created profile...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  let { data: existingProfile, error: checkError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .maybeSingle();
  
  if (!checkError && existingProfile) {
    console.log('✅ Profile found - created by trigger!');
    return {
      success: true,
      profile: existingProfile,
      method: 'trigger'
    };
  }
  
  // Method 2: Try RPC function
  console.log('🔄 Method 2: Trying RPC function...');
  try {
    const { data: rpcResult, error: rpcError } = await supabase.rpc('create_user_profile', {
      user_id: userId,
      user_email: userData.email,
      user_name: userData.full_name,
      user_phone: userData.phone_number,
      user_role: userData.role
    });
    
    if (!rpcError && rpcResult?.success) {
      console.log('✅ Profile created via RPC function!');
      return {
        success: true,
        profile: rpcResult.profile,
        method: 'rpc'
      };
    }
    
    console.log('⚠️ RPC method failed:', rpcError?.message || 'Unknown error');
  } catch (rpcErr) {
    console.log('⚠️ RPC method exception:', rpcErr.message);
  }
  
  // Method 3: Direct insert
  console.log('🔄 Method 3: Direct profile insert...');
  try {
    const { data: manualProfile, error: manualError } = await supabase
      .from('profiles')
      .insert({
        id: userId,
        email: userData.email,
        full_name: userData.full_name,
        phone_number: userData.phone_number,
        role: userData.role,
        email_verified: false,
        is_active: true
      })
      .select()
      .single();
    
    if (!manualError && manualProfile) {
      console.log('✅ Profile created via direct insert!');
      return {
        success: true,
        profile: manualProfile,
        method: 'manual'
      };
    }
    
    console.error('❌ Direct insert failed:', manualError);
  } catch (manualErr) {
    console.error('❌ Direct insert exception:', manualErr);
  }
  
  // All methods failed
  return {
    success: false,
    error: 'All profile creation methods failed'
  };
}

async function testMultipleSignups() {
  console.log('\n🧪 Testing Multiple Signups');
  console.log('='.repeat(50));
  
  const results = [];
  
  for (let i = 1; i <= 3; i++) {
    console.log(`\n📧 Test ${i}/3: Creating user ${i}...`);
    
    const testEmail = `multi-test-${i}-${Date.now()}@example.com`;
    const result = await simulateFrontendSignup();
    
    results.push({
      test: i,
      email: testEmail,
      success: result
    });
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 Multiple Signup Results:');
  results.forEach(result => {
    console.log(`Test ${result.test}: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  });
  
  const successCount = results.filter(r => r.success).length;
  console.log(`\n🎯 Success Rate: ${successCount}/${results.length} (${Math.round(successCount/results.length*100)}%)`);
  
  return successCount === results.length;
}

async function main() {
  console.log('🚀 TESTING FRONTEND INTEGRATION WITH MANUAL PROFILE CREATION\n');
  
  // Test 1: Single signup simulation
  const singleTest = await simulateFrontendSignup();
  
  // Test 2: Multiple signups
  const multipleTest = await testMultipleSignups();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 FINAL RESULTS:');
  console.log('='.repeat(50));
  
  if (singleTest && multipleTest) {
    console.log('🎉 🎉 🎉 ALL TESTS PASSED! 🎉 🎉 🎉');
    console.log('✅ Frontend signup simulation works perfectly!');
    console.log('✅ Manual profile creation is reliable!');
    console.log('✅ Multiple fallback methods ensure 100% success!');
    console.log('✅ Login and profile access work correctly!');
    console.log('\n🚀 Your frontend signup implementation is ready for production!');
    console.log('\n💡 IMPLEMENTATION STEPS:');
    console.log('1. Replace SupabaseRegisterForm with EnhancedRegisterForm');
    console.log('2. The enhanced auth service will handle profile creation automatically');
    console.log('3. Users will see progress indicators during signup');
    console.log('4. Profile creation is guaranteed to work with multiple fallbacks');
  } else {
    console.log('❌ Some tests failed. Issues to address:');
    if (!singleTest) console.log('- Single signup simulation failed');
    if (!multipleTest) console.log('- Multiple signup test failed');
    console.log('\n🔧 Next steps:');
    console.log('1. Run the complete-trigger-solution.sql script in Supabase');
    console.log('2. Check Supabase logs for any errors');
    console.log('3. Verify RLS policies are correctly configured');
  }
}

main().catch(console.error);
