#!/usr/bin/env node

/**
 * Create Departments and Fix Doctor Records
 * This script creates departments and then creates missing doctor records
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const departments = [
  {
    department_id: 'DEPT001',
    name: '<PERSON><PERSON><PERSON>',
    description: 'K<PERSON>a N<PERSON>i tổng hợp',
    head_doctor_id: null,
    location: 'Tầng 2',
    phone_number: '0123456789',
    email: '<EMAIL>'
  },
  {
    department_id: 'DEPT002',
    name: '<PERSON><PERSON><PERSON> m<PERSON>',
    description: '<PERSON><PERSON><PERSON> m<PERSON> can thiệp',
    head_doctor_id: null,
    location: 'Tầng 3',
    phone_number: '0123456790',
    email: '<EMAIL>'
  },
  {
    department_id: 'DEPT003',
    name: 'Khoa Nhi',
    description: 'Khoa Nhi tổng hợp',
    head_doctor_id: null,
    location: 'Tầng 4',
    phone_number: '0123456791',
    email: '<EMAIL>'
  },
  {
    department_id: 'DEPT004',
    name: 'Khoa Chấn thương Chỉnh hình',
    description: 'Khoa Chấn thương và Chỉnh hình',
    head_doctor_id: null,
    location: 'Tầng 5',
    phone_number: '0123456792',
    email: '<EMAIL>'
  },
  {
    department_id: 'DEPT005',
    name: 'Khoa Cấp cứu',
    description: 'Khoa Cấp cứu và Hồi sức',
    head_doctor_id: null,
    location: 'Tầng 1',
    phone_number: '0123456793',
    email: '<EMAIL>'
  }
];

async function createDepartments() {
  console.log('🏥 Creating departments...');
  
  // Check if departments already exist
  const { data: existingDepts, error: checkError } = await supabase
    .from('departments')
    .select('department_id, name');
  
  if (checkError) {
    console.error('❌ Error checking existing departments:', checkError);
    return false;
  }
  
  if (existingDepts && existingDepts.length > 0) {
    console.log(`✅ Found ${existingDepts.length} existing departments`);
    existingDepts.forEach(dept => {
      console.log(`   - ${dept.name} (${dept.department_id})`);
    });
    return true;
  }
  
  // Create departments
  const { data, error } = await supabase
    .from('departments')
    .insert(departments)
    .select();
  
  if (error) {
    console.error('❌ Error creating departments:', error);
    return false;
  }
  
  console.log(`✅ Created ${data.length} departments successfully!`);
  data.forEach(dept => {
    console.log(`   - ${dept.name} (${dept.department_id})`);
  });
  
  return true;
}

async function createMissingDoctorRecords() {
  console.log('👨‍⚕️ Creating missing doctor records...');
  
  // Get doctor profiles without doctor records
  const { data: doctorProfiles, error: profileError } = await supabase
    .from('profiles')
    .select('id, email, full_name, role, phone_number')
    .eq('role', 'doctor');
  
  if (profileError) {
    console.error('❌ Error fetching doctor profiles:', profileError);
    return false;
  }
  
  if (!doctorProfiles || doctorProfiles.length === 0) {
    console.log('ℹ️ No doctor profiles found');
    return true;
  }
  
  // Get existing doctor records
  const { data: existingDoctors, error: doctorError } = await supabase
    .from('doctors')
    .select('profile_id');
  
  if (doctorError) {
    console.error('❌ Error fetching existing doctors:', doctorError);
    return false;
  }
  
  const existingProfileIds = existingDoctors ? existingDoctors.map(d => d.profile_id) : [];
  
  // Find missing doctor records
  const missingDoctors = doctorProfiles.filter(profile => 
    !existingProfileIds.includes(profile.id)
  );
  
  if (missingDoctors.length === 0) {
    console.log('✅ All doctor profiles already have doctor records');
    return true;
  }
  
  console.log(`🔧 Creating ${missingDoctors.length} missing doctor records...`);
  
  // Get default department
  const { data: departments, error: deptError } = await supabase
    .from('departments')
    .select('department_id, name')
    .limit(1);
  
  if (deptError || !departments || departments.length === 0) {
    console.error('❌ No departments found');
    return false;
  }
  
  const defaultDepartment = departments[0];
  console.log(`🏥 Using default department: ${defaultDepartment.name}`);
  
  let successCount = 0;
  
  for (const doctor of missingDoctors) {
    try {
      // Generate unique doctor ID
      const timestamp = Date.now().toString().slice(-6);
      const doctorId = `DOC${timestamp}`;
      
      // Generate unique license number
      const licenseNumber = `BS${Math.floor(Math.random() * 900000) + 100000}`;
      
      const doctorRecord = {
        doctor_id: doctorId,
        profile_id: doctor.id,
        license_number: licenseNumber,
        specialization: 'Tổng quát',
        qualification: 'Bác sĩ',
        experience_years: 1,
        consultation_fee: 200000,
        department_id: defaultDepartment.department_id,
        status: 'active',
        bio: `Bác sĩ ${doctor.full_name} - Chuyên khoa tổng quát`,
        languages_spoken: ['Vietnamese', 'English'],
        working_hours: {
          monday: '08:00-17:00',
          tuesday: '08:00-17:00',
          wednesday: '08:00-17:00',
          thursday: '08:00-17:00',
          friday: '08:00-17:00',
          saturday: '08:00-12:00'
        }
      };
      
      const { data, error } = await supabase
        .from('doctors')
        .insert(doctorRecord)
        .select()
        .single();
      
      if (error) {
        console.error(`❌ Failed to create doctor record for ${doctor.full_name}:`, error);
      } else {
        console.log(`✅ Created doctor record: ${doctor.full_name} (${doctorId})`);
        successCount++;
      }
      
      // Small delay to ensure unique timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
      
    } catch (err) {
      console.error(`❌ Exception creating doctor record for ${doctor.full_name}:`, err);
    }
  }
  
  console.log(`📊 Successfully created ${successCount}/${missingDoctors.length} doctor records`);
  return successCount > 0;
}

async function verifyResults() {
  console.log('🔍 Verifying results...');
  
  // Check departments
  const { data: departments, error: deptError } = await supabase
    .from('departments')
    .select('department_id, name');
  
  if (deptError) {
    console.error('❌ Error checking departments:', deptError);
  } else {
    console.log(`✅ Departments: ${departments.length} found`);
  }
  
  // Check doctor profiles vs doctor records
  const { data: doctorProfiles, error: profileError } = await supabase
    .from('profiles')
    .select('id, full_name, role')
    .eq('role', 'doctor');
  
  const { data: doctorRecords, error: recordError } = await supabase
    .from('doctors')
    .select('doctor_id, profile_id, specialization');
  
  if (profileError || recordError) {
    console.error('❌ Error checking doctor data');
  } else {
    console.log(`✅ Doctor Profiles: ${doctorProfiles.length}`);
    console.log(`✅ Doctor Records: ${doctorRecords.length}`);
    
    if (doctorProfiles.length === doctorRecords.length) {
      console.log('🎉 All doctor profiles have corresponding doctor records!');
    } else {
      console.log('⚠️ Mismatch between profiles and records');
    }
  }
}

async function main() {
  console.log('🚀 Starting departments and doctors creation...');
  
  try {
    // 1. Create departments
    const deptSuccess = await createDepartments();
    if (!deptSuccess) {
      console.error('❌ Failed to create departments');
      return;
    }
    
    // 2. Create missing doctor records
    const doctorSuccess = await createMissingDoctorRecords();
    if (!doctorSuccess) {
      console.error('❌ Failed to create doctor records');
      return;
    }
    
    // 3. Verify results
    await verifyResults();
    
    console.log('\n🎉 SUCCESS! Departments and doctor records created successfully!');
    console.log('✅ Doctor registration should now work properly');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

main().catch(console.error);
