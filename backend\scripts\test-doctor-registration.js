#!/usr/bin/env node

/**
 * Test Doctor Registration
 * This script tests the complete doctor registration flow
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDoctorRegistration() {
  console.log('🧪 Testing doctor registration flow...');
  
  const testEmail = `test-doctor-${Date.now()}@hospital.com`;
  const testPassword = 'TestPassword123!';
  const testName = 'Dr. Test Registration';
  
  try {
    // 1. Create auth user with doctor role
    console.log('👤 Creating auth user...');
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true,
      user_metadata: {
        full_name: testName,
        role: 'doctor',
        phone_number: '0987654321'
      }
    });

    if (authError) {
      console.error('❌ Auth user creation failed:', authError);
      return false;
    }

    console.log('✅ Auth user created:', authData.user.id);

    // 2. Wait for trigger to create profile
    console.log('⏳ Waiting for profile trigger...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 3. Check if profile was created
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError || !profile) {
      console.error('❌ Profile not created by trigger:', profileError);
      
      // Manual profile creation as fallback
      console.log('🔧 Creating profile manually...');
      const { data: manualProfile, error: manualError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email: testEmail,
          full_name: testName,
          role: 'doctor',
          phone_number: '0987654321',
          is_active: true,
          email_verified: true
        })
        .select()
        .single();

      if (manualError) {
        console.error('❌ Manual profile creation failed:', manualError);
        await cleanup(authData.user.id);
        return false;
      }

      console.log('✅ Profile created manually');
    } else {
      console.log('✅ Profile created by trigger');
    }

    // 4. Wait for doctor record trigger
    console.log('⏳ Waiting for doctor record trigger...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 5. Check if doctor record was created
    const { data: doctorRecord, error: doctorError } = await supabase
      .from('doctors')
      .select('*')
      .eq('profile_id', authData.user.id)
      .single();

    if (doctorError || !doctorRecord) {
      console.error('❌ Doctor record not created by trigger:', doctorError);
      
      // Manual doctor record creation as fallback
      console.log('🔧 Creating doctor record manually...');
      
      // Get default department
      const { data: department } = await supabase
        .from('departments')
        .select('department_id')
        .limit(1)
        .single();

      if (!department) {
        console.error('❌ No departments found');
        await cleanup(authData.user.id);
        return false;
      }

      const doctorId = `DOC${Date.now().toString().slice(-6)}`;
      const licenseNumber = `BS${Math.floor(Math.random() * 900000) + 100000}`;

      const { data: manualDoctor, error: manualDoctorError } = await supabase
        .from('doctors')
        .insert({
          doctor_id: doctorId,
          profile_id: authData.user.id,
          license_number: licenseNumber,
          specialization: 'Tổng quát',
          qualification: 'Bác sĩ',
          experience_years: 1,
          consultation_fee: 200000,
          department_id: department.department_id,
          status: 'active',
          bio: `Bác sĩ ${testName}`,
          languages_spoken: ['Vietnamese'],
          working_hours: {
            monday: '08:00-17:00',
            tuesday: '08:00-17:00',
            wednesday: '08:00-17:00',
            thursday: '08:00-17:00',
            friday: '08:00-17:00'
          }
        })
        .select()
        .single();

      if (manualDoctorError) {
        console.error('❌ Manual doctor record creation failed:', manualDoctorError);
        await cleanup(authData.user.id);
        return false;
      }

      console.log('✅ Doctor record created manually:', manualDoctor.doctor_id);
    } else {
      console.log('✅ Doctor record created by trigger:', doctorRecord.doctor_id);
    }

    // 6. Verify complete registration
    const { data: completeData, error: verifyError } = await supabase
      .from('profiles')
      .select(`
        *,
        doctors (
          doctor_id,
          license_number,
          specialization,
          qualification,
          department_id,
          departments (
            name
          )
        )
      `)
      .eq('id', authData.user.id)
      .single();

    if (verifyError || !completeData) {
      console.error('❌ Verification failed:', verifyError);
      await cleanup(authData.user.id);
      return false;
    }

    console.log('🎉 Complete doctor registration verified!');
    console.log('📊 Registration Details:');
    console.log(`   Profile ID: ${completeData.id}`);
    console.log(`   Name: ${completeData.full_name}`);
    console.log(`   Email: ${completeData.email}`);
    console.log(`   Role: ${completeData.role}`);
    if (completeData.doctors) {
      console.log(`   Doctor ID: ${completeData.doctors.doctor_id}`);
      console.log(`   License: ${completeData.doctors.license_number}`);
      console.log(`   Specialization: ${completeData.doctors.specialization}`);
      console.log(`   Department: ${completeData.doctors.departments?.name}`);
    }

    // 7. Clean up test data
    await cleanup(authData.user.id);
    
    return true;

  } catch (error) {
    console.error('❌ Test failed with exception:', error);
    return false;
  }
}

async function cleanup(userId) {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Delete doctor record
    await supabase.from('doctors').delete().eq('profile_id', userId);
    
    // Delete profile
    await supabase.from('profiles').delete().eq('id', userId);
    
    // Delete auth user
    await supabase.auth.admin.deleteUser(userId);
    
    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('⚠️ Cleanup error:', error);
  }
}

async function checkSystemStatus() {
  console.log('🔍 Checking system status...');
  
  try {
    // Check enum function
    const { data: enumTest, error: enumError } = await supabase.rpc('validate_enum_value', {
      value_to_check: 'doctor',
      enum_type: 'user_role'
    });

    console.log(`Enum Function: ${enumError ? '❌ Error' : '✅ Working'}`);
    if (enumError) console.log(`   Error: ${enumError.message}`);

    // Check departments
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select('department_id, name');

    console.log(`Departments: ${deptError ? '❌ Error' : `✅ ${departments.length} found`}`);

    // Check existing doctors
    const { data: doctors, error: doctorError } = await supabase
      .from('doctors')
      .select('doctor_id, profile_id');

    console.log(`Doctor Records: ${doctorError ? '❌ Error' : `✅ ${doctors.length} found`}`);

    return !enumError && !deptError && !doctorError;

  } catch (error) {
    console.error('❌ System check failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting doctor registration test...');
  
  // 1. Check system status
  const systemOk = await checkSystemStatus();
  if (!systemOk) {
    console.log('❌ System not ready for testing');
    return;
  }

  // 2. Test doctor registration
  const testPassed = await testDoctorRegistration();
  
  console.log('\n📊 Test Results:');
  console.log(`Doctor Registration: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (testPassed) {
    console.log('\n🎉 SUCCESS! Doctor registration is working correctly!');
    console.log('✅ Users can now register as doctors and get proper doctor records');
  } else {
    console.log('\n❌ FAILED! Doctor registration needs more work');
  }
}

main().catch(console.error);
