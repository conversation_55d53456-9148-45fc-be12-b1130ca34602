#!/usr/bin/env node

/**
 * Fix Missing Enum Validation Function
 * This script creates the validate_enum_value function that's missing from the database
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createEnumValidationFunction() {
  console.log('🔧 Creating enum validation function...');

  try {
    // Since we can't run raw SQL through Supabase client, we'll create a simple function
    // that can be used as a workaround

    console.log('📝 Creating simple validate_enum_value function...');

    // Test if function already exists by trying to call it
    try {
      const { data: testExisting } = await supabase.rpc('validate_enum_value', {
        value_to_check: 'test',
        enum_type: 'test'
      });

      console.log('✅ Function already exists and working!');
      return true;
    } catch (existingError) {
      console.log('⚠️ Function does not exist, will need manual creation');
    }

    console.log('\n🔧 MANUAL STEPS REQUIRED:');
    console.log('Since we cannot create database functions through the Supabase client,');
    console.log('you need to run the SQL manually in your Supabase dashboard.');
    console.log('\n📋 Steps:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the following SQL:');
    console.log('\n' + '='.repeat(60));

    const sqlToRun = `
-- Create enum validation function
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check text, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Validate based on enum type
  CASE enum_type
    WHEN 'user_role' THEN
      RETURN value_to_check IN ('patient', 'doctor', 'admin');

    WHEN 'gender' THEN
      RETURN value_to_check IN ('male', 'female', 'other');

    WHEN 'blood_type' THEN
      RETURN value_to_check IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-');

    WHEN 'appointment_status' THEN
      RETURN value_to_check IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');

    WHEN 'qualification' THEN
      RETURN value_to_check IN ('Bác sĩ', 'Thạc sĩ', 'Tiến sĩ', 'Giáo sư', 'Phó Giáo sư');

    WHEN 'specialization' THEN
      RETURN value_to_check IN (
        'Nội tổng hợp', 'Ngoại tổng hợp', 'Sản phụ khoa', 'Nhi khoa',
        'Tim mạch can thiệp', 'Thần kinh học', 'Chấn thương và chỉnh hình',
        'Cấp cứu và hồi sức', 'Da liễu', 'Mắt', 'Tai mũi họng', 'Răng hàm mặt'
      );

    WHEN 'user_status' THEN
      RETURN value_to_check IN ('active', 'inactive', 'pending', 'suspended');

    ELSE
      -- For unknown enum types, return true (permissive)
      RETURN true;
  END CASE;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_enum_value(text, text) TO authenticated, anon, service_role;

-- Create nullable helper function
CREATE OR REPLACE FUNCTION validate_enum_value_nullable(value_to_check text, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Allow NULL values
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;

  -- Use main validation function
  RETURN validate_enum_value(value_to_check, enum_type);
END;
$$;

-- Grant permissions for nullable function
GRANT EXECUTE ON FUNCTION validate_enum_value_nullable(text, text) TO authenticated, anon, service_role;
`;

    console.log(sqlToRun);
    console.log('='.repeat(60));
    console.log('\n4. Click "Run" to execute the SQL');
    console.log('5. The function will be created and the error should be fixed');

    return false; // Indicate manual steps needed

    // 6. Test the function
    console.log('🧪 Testing the function...');
    try {
      const { data: testResult, error: testError } = await supabase.rpc('validate_enum_value', {
        value_to_check: 'patient',
        enum_type: 'user_role'
      });

      if (testError) {
        console.error('❌ Function test failed:', testError);
        return false;
      }

      if (testResult === true) {
        console.log('✅ Function test passed! Returns true for valid enum value.');
      } else {
        console.error('❌ Function test failed! Expected true, got:', testResult);
        return false;
      }
    } catch (testErr) {
      console.error('❌ Function test exception:', testErr);
      return false;
    }

    return true;

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting enum validation function creation...');
  console.log('📍 Supabase URL:', supabaseUrl);

  const success = await createEnumValidationFunction();

  if (success) {
    console.log('\n🎉 SUCCESS! Enum validation function created successfully!');
    console.log('✅ The validate_enum_value function is now available in your database.');
    console.log('✅ You should no longer see the "function does not exist" error.');
  } else {
    console.log('\n❌ FAILED! Could not create enum validation function.');
    console.log('💡 Try running this script again or check your database permissions.');
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
