-- =====================================================
-- CREATE DOCTOR AUTO-CREATION TRIGGER
-- =====================================================
-- This trigger automatically creates doctor records when a profile with role 'doctor' is created

-- 1. Create function to auto-create doctor records
CREATE OR REPLACE FUNCTION auto_create_doctor_record()
RETURNS TRIGGER AS $$
DECLARE
  default_dept_id text;
  new_doctor_id text;
  new_license_number text;
  random_suffix text;
BEGIN
  -- Only proceed if role is 'doctor'
  IF NEW.role = 'doctor' THEN
    
    -- Get default department (Khoa Nội)
    SELECT department_id INTO default_dept_id 
    FROM departments 
    WHERE department_id = 'DEPT001'
    LIMIT 1;
    
    -- If no specific department found, get any department
    IF default_dept_id IS NULL THEN
      SELECT department_id INTO default_dept_id 
      FROM departments 
      LIMIT 1;
    END IF;
    
    -- Generate unique doctor ID using timestamp
    new_doctor_id := 'DOC' || LPAD(EXTRACT(epoch FROM NOW())::bigint::text, 6, '0');
    
    -- Generate unique license number
    random_suffix := LPAD((RANDOM() * 900000 + 100000)::int::text, 6, '0');
    new_license_number := 'BS' || random_suffix;
    
    -- Insert doctor record
    INSERT INTO doctors (
      doctor_id,
      profile_id,
      license_number,
      specialization,
      qualification,
      experience_years,
      consultation_fee,
      department_id,
      status,
      bio,
      languages_spoken,
      working_hours
    ) VALUES (
      new_doctor_id,
      NEW.id,
      new_license_number,
      'Tổng quát',
      'Bác sĩ',
      1,
      200000,
      default_dept_id,
      'active',
      'Bác sĩ ' || NEW.full_name,
      ARRAY['Vietnamese'],
      '{"monday": "08:00-17:00", "tuesday": "08:00-17:00", "wednesday": "08:00-17:00", "thursday": "08:00-17:00", "friday": "08:00-17:00", "saturday": "08:00-12:00"}'::jsonb
    );
    
    -- Log the creation
    RAISE NOTICE 'Auto-created doctor record % for profile %', new_doctor_id, NEW.id;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Grant permissions
GRANT EXECUTE ON FUNCTION auto_create_doctor_record() TO authenticated, anon, service_role;

-- 3. Create trigger
DROP TRIGGER IF EXISTS auto_create_doctor_on_profile_insert ON profiles;
CREATE TRIGGER auto_create_doctor_on_profile_insert
  AFTER INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_doctor_record();

-- 4. Also create trigger for profile updates (when role changes to doctor)
DROP TRIGGER IF EXISTS auto_create_doctor_on_profile_update ON profiles;
CREATE TRIGGER auto_create_doctor_on_profile_update
  AFTER UPDATE ON profiles
  FOR EACH ROW
  WHEN (OLD.role != 'doctor' AND NEW.role = 'doctor')
  EXECUTE FUNCTION auto_create_doctor_record();

-- 5. Test the trigger (optional)
-- This will be commented out for safety
/*
-- Test with a dummy profile
INSERT INTO profiles (
  id, 
  email, 
  full_name, 
  role, 
  phone_number, 
  is_active, 
  email_verified
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  'Test Trigger Doctor',
  'doctor',
  '**********',
  true,
  true
);

-- Check if doctor record was created
SELECT 
  p.full_name as profile_name,
  d.doctor_id,
  d.license_number,
  d.specialization
FROM profiles p
LEFT JOIN doctors d ON p.id = d.profile_id
WHERE p.email = '<EMAIL>';

-- Clean up test data
DELETE FROM doctors WHERE profile_id IN (
  SELECT id FROM profiles WHERE email = '<EMAIL>'
);
DELETE FROM profiles WHERE email = '<EMAIL>';
*/

-- 6. Verify trigger creation
SELECT 
  trigger_name,
  event_object_table,
  action_timing,
  event_manipulation
FROM information_schema.triggers 
WHERE trigger_name LIKE '%auto_create_doctor%';

SELECT 'Doctor auto-creation trigger created successfully!' as status;
