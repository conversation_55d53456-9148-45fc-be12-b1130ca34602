#!/usr/bin/env node

/**
 * Fix Doctor Registration Issues
 * This script fixes both the enum validation error and missing doctor profile creation
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkCurrentIssues() {
  console.log('🔍 Checking current issues...');
  
  // Check if validate_enum_value function exists
  try {
    const { data, error } = await supabase.rpc('validate_enum_value', {
      value_to_check: 'patient',
      enum_type: 'user_role'
    });
    
    if (error) {
      console.log('❌ validate_enum_value function issue:', error.message);
      return { enumFunction: false, error: error.message };
    } else {
      console.log('✅ validate_enum_value function working');
      return { enumFunction: true };
    }
  } catch (err) {
    console.log('❌ validate_enum_value function error:', err.message);
    return { enumFunction: false, error: err.message };
  }
}

async function checkDoctorProfiles() {
  console.log('👨‍⚕️ Checking doctor profiles...');
  
  try {
    // Get profiles with role 'doctor'
    const { data: doctorProfiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, full_name, role')
      .eq('role', 'doctor');
    
    if (profileError) {
      console.error('❌ Error fetching doctor profiles:', profileError);
      return { doctorProfiles: [], doctorRecords: [] };
    }
    
    console.log(`📊 Found ${doctorProfiles.length} doctor profiles`);
    
    // Get corresponding doctor records
    const { data: doctorRecords, error: doctorError } = await supabase
      .from('doctors')
      .select('doctor_id, profile_id, specialization, license_number');
    
    if (doctorError) {
      console.error('❌ Error fetching doctor records:', doctorError);
      return { doctorProfiles, doctorRecords: [] };
    }
    
    console.log(`📊 Found ${doctorRecords.length} doctor records`);
    
    // Find missing doctor records
    const missingDoctors = doctorProfiles.filter(profile => 
      !doctorRecords.some(doctor => doctor.profile_id === profile.id)
    );
    
    if (missingDoctors.length > 0) {
      console.log(`⚠️ Found ${missingDoctors.length} doctor profiles without doctor records:`);
      missingDoctors.forEach(doctor => {
        console.log(`   - ${doctor.full_name} (${doctor.email})`);
      });
    } else {
      console.log('✅ All doctor profiles have corresponding doctor records');
    }
    
    return { doctorProfiles, doctorRecords, missingDoctors };
    
  } catch (err) {
    console.error('❌ Error checking doctor profiles:', err);
    return { doctorProfiles: [], doctorRecords: [], missingDoctors: [] };
  }
}

async function createMissingDoctorRecords(missingDoctors) {
  if (!missingDoctors || missingDoctors.length === 0) {
    console.log('✅ No missing doctor records to create');
    return true;
  }
  
  console.log(`🔧 Creating ${missingDoctors.length} missing doctor records...`);
  
  // Get available departments
  const { data: departments, error: deptError } = await supabase
    .from('departments')
    .select('department_id, name');
  
  if (deptError || !departments || departments.length === 0) {
    console.error('❌ No departments found. Cannot create doctor records.');
    return false;
  }
  
  const defaultDepartment = departments[0]; // Use first department as default
  console.log(`🏥 Using default department: ${defaultDepartment.name} (${defaultDepartment.department_id})`);
  
  let successCount = 0;
  
  for (const doctor of missingDoctors) {
    try {
      // Generate doctor ID
      const doctorId = `DOC${String(Date.now()).slice(-6)}`;
      
      // Generate license number
      const licenseNumber = `BS${String(Math.floor(Math.random() * 900000) + 100000)}`;
      
      const doctorRecord = {
        doctor_id: doctorId,
        profile_id: doctor.id,
        license_number: licenseNumber,
        specialization: 'Tổng quát', // Default specialization
        qualification: 'Bác sĩ',
        experience_years: 1,
        consultation_fee: 200000,
        department_id: defaultDepartment.department_id,
        status: 'active',
        bio: `Bác sĩ ${doctor.full_name}`,
        languages_spoken: ['Vietnamese'],
        working_hours: {
          monday: '08:00-17:00',
          tuesday: '08:00-17:00',
          wednesday: '08:00-17:00',
          thursday: '08:00-17:00',
          friday: '08:00-17:00'
        }
      };
      
      const { data, error } = await supabase
        .from('doctors')
        .insert(doctorRecord)
        .select()
        .single();
      
      if (error) {
        console.error(`❌ Failed to create doctor record for ${doctor.full_name}:`, error);
      } else {
        console.log(`✅ Created doctor record for ${doctor.full_name} (ID: ${doctorId})`);
        successCount++;
      }
      
    } catch (err) {
      console.error(`❌ Exception creating doctor record for ${doctor.full_name}:`, err);
    }
  }
  
  console.log(`📊 Successfully created ${successCount}/${missingDoctors.length} doctor records`);
  return successCount === missingDoctors.length;
}

async function createDoctorTrigger() {
  console.log('🔧 Creating trigger to auto-create doctor records...');
  
  const triggerSQL = `
-- Create function to auto-create doctor records
CREATE OR REPLACE FUNCTION auto_create_doctor_record()
RETURNS TRIGGER AS $$
DECLARE
  default_dept_id text;
  new_doctor_id text;
  new_license_number text;
BEGIN
  -- Only proceed if role is 'doctor'
  IF NEW.role = 'doctor' THEN
    
    -- Get default department
    SELECT department_id INTO default_dept_id 
    FROM departments 
    LIMIT 1;
    
    -- Generate doctor ID
    new_doctor_id := 'DOC' || LPAD(EXTRACT(epoch FROM NOW())::bigint::text, 6, '0');
    
    -- Generate license number
    new_license_number := 'BS' || LPAD((RANDOM() * 900000 + 100000)::int::text, 6, '0');
    
    -- Insert doctor record
    INSERT INTO doctors (
      doctor_id,
      profile_id,
      license_number,
      specialization,
      qualification,
      experience_years,
      consultation_fee,
      department_id,
      status,
      bio,
      languages_spoken,
      working_hours
    ) VALUES (
      new_doctor_id,
      NEW.id,
      new_license_number,
      'Tổng quát',
      'Bác sĩ',
      1,
      200000,
      default_dept_id,
      'active',
      'Bác sĩ ' || NEW.full_name,
      ARRAY['Vietnamese'],
      '{"monday": "08:00-17:00", "tuesday": "08:00-17:00", "wednesday": "08:00-17:00", "thursday": "08:00-17:00", "friday": "08:00-17:00"}'::jsonb
    );
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
DROP TRIGGER IF EXISTS auto_create_doctor_on_profile_insert ON profiles;
CREATE TRIGGER auto_create_doctor_on_profile_insert
  AFTER INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_doctor_record();
`;

  console.log('\n🔧 MANUAL STEP REQUIRED:');
  console.log('Please run this SQL in your Supabase dashboard:');
  console.log('='.repeat(60));
  console.log(triggerSQL);
  console.log('='.repeat(60));
  
  return true;
}

async function main() {
  console.log('🚀 Starting doctor registration fix...');
  
  // 1. Check current issues
  const issues = await checkCurrentIssues();
  
  // 2. Check doctor profiles
  const { doctorProfiles, doctorRecords, missingDoctors } = await checkDoctorProfiles();
  
  // 3. Create missing doctor records
  if (missingDoctors && missingDoctors.length > 0) {
    await createMissingDoctorRecords(missingDoctors);
  }
  
  // 4. Create trigger for future registrations
  await createDoctorTrigger();
  
  console.log('\n📊 Summary:');
  console.log(`Enum Function: ${issues.enumFunction ? '✅ Working' : '❌ Needs Fix'}`);
  console.log(`Doctor Profiles: ${doctorProfiles.length} found`);
  console.log(`Doctor Records: ${doctorRecords.length} found`);
  console.log(`Missing Records: ${missingDoctors ? missingDoctors.length : 0} ${missingDoctors && missingDoctors.length > 0 ? '(fixed)' : ''}`);
  
  if (!issues.enumFunction) {
    console.log('\n⚠️ Enum validation function still needs to be fixed manually.');
    console.log('Please run the SQL from the previous script in your Supabase dashboard.');
  }
  
  console.log('\n🎉 Doctor registration fix completed!');
}

main().catch(console.error);
