-- =====================================================
-- CREATE PROPER ENUM VALIDATION FUNCTION
-- =====================================================
-- This creates a working enum validation function for your hospital system

-- 1. Drop existing problematic function
DROP FUNCTION IF EXISTS validate_enum_value CASCADE;

-- 2. Create proper enum validation function
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check text, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Validate based on enum type
  CASE enum_type
    WHEN 'user_role' THEN
      RETURN value_to_check IN ('patient', 'doctor', 'admin');
    
    WHEN 'gender' THEN
      RETURN value_to_check IN ('male', 'female', 'other');
    
    WHEN 'blood_type' THEN
      RETURN value_to_check IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-');
    
    WHEN 'appointment_status' THEN
      RETURN value_to_check IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
    
    WHEN 'qualification' THEN
      RETURN value_to_check IN ('Bác sĩ', 'Thạc sĩ', 'Tiến sĩ', 'Giáo sư', 'Phó Giáo sư');
    
    WHEN 'specialization' THEN
      RETURN value_to_check IN (
        'Nội tổng hợp', 'Ngoại tổng hợp', 'Sản phụ khoa', 'Nhi khoa',
        'Tim mạch can thiệp', 'Thần kinh học', 'Chấn thương và chỉnh hình',
        'Cấp cứu và hồi sức', 'Da liễu', 'Mắt', 'Tai mũi họng', 'Răng hàm mặt'
      );
    
    WHEN 'user_status' THEN
      RETURN value_to_check IN ('active', 'inactive', 'pending', 'suspended');
    
    ELSE
      -- For unknown enum types, return true (permissive)
      RETURN true;
  END CASE;
END;
$$;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION validate_enum_value(text, text) TO authenticated, anon, service_role;

-- 4. Create helper function for NULL values
CREATE OR REPLACE FUNCTION validate_enum_value_nullable(value_to_check text, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Allow NULL values
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Use main validation function
  RETURN validate_enum_value(value_to_check, enum_type);
END;
$$;

GRANT EXECUTE ON FUNCTION validate_enum_value_nullable(text, text) TO authenticated, anon, service_role;

-- 5. Add useful constraints to profiles table (optional)
-- Uncomment these if you want to enforce enum validation:

/*
-- Role validation
ALTER TABLE profiles 
ADD CONSTRAINT IF NOT EXISTS check_valid_role 
CHECK (validate_enum_value(role, 'user_role'));

-- Gender validation  
ALTER TABLE profiles 
ADD CONSTRAINT IF NOT EXISTS check_valid_gender 
CHECK (validate_enum_value_nullable(gender, 'gender'));

-- Blood type validation
ALTER TABLE profiles 
ADD CONSTRAINT IF NOT EXISTS check_valid_blood_type 
CHECK (validate_enum_value_nullable(blood_type, 'blood_type'));
*/

-- 6. Test the function
SELECT 
  'Testing enum validation...' as test_start,
  validate_enum_value('patient', 'user_role') as test_role_valid,
  validate_enum_value('invalid_role', 'user_role') as test_role_invalid,
  validate_enum_value('male', 'gender') as test_gender_valid,
  validate_enum_value_nullable(NULL, 'gender') as test_null_allowed;

-- 7. Check if function is working
SELECT 
  proname as function_name,
  pg_get_function_arguments(oid) as arguments
FROM pg_proc 
WHERE proname LIKE '%validate_enum%';

SELECT 'Proper enum validation function created!' as status;
