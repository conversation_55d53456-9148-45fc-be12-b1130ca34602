#!/usr/bin/env node

/**
 * Find Problematic Triggers and Constraints
 * This script searches for triggers, constraints, or functions that might be causing the validate_enum_value error
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function findTriggersWithValidateEnum() {
  console.log('🔍 Searching for triggers that use validate_enum_value...');
  
  try {
    // Query to find triggers that reference validate_enum_value
    const { data, error } = await supabase.rpc('sql', {
      query: `
        SELECT 
          trigger_name,
          event_object_table,
          action_statement,
          action_timing,
          event_manipulation
        FROM information_schema.triggers 
        WHERE action_statement LIKE '%validate_enum_value%'
           OR action_statement LIKE '%last_sign_in%';
      `
    });

    if (error) {
      console.log('⚠️ Cannot query triggers directly. Will use alternative method.');
      return await findTriggersAlternative();
    }

    if (data && data.length > 0) {
      console.log(`📋 Found ${data.length} triggers using validate_enum_value or last_sign_in:`);
      data.forEach(trigger => {
        console.log(`   - ${trigger.trigger_name} on ${trigger.event_object_table}`);
        console.log(`     Timing: ${trigger.action_timing} ${trigger.event_manipulation}`);
        console.log(`     Statement: ${trigger.action_statement.substring(0, 100)}...`);
      });
      return data;
    } else {
      console.log('✅ No triggers found using validate_enum_value or last_sign_in');
      return [];
    }

  } catch (error) {
    console.error('❌ Error searching triggers:', error);
    return await findTriggersAlternative();
  }
}

async function findTriggersAlternative() {
  console.log('🔍 Using alternative method to find triggers...');
  
  try {
    // Get all triggers
    const { data, error } = await supabase.rpc('sql', {
      query: `
        SELECT 
          trigger_name,
          event_object_table,
          action_timing,
          event_manipulation
        FROM information_schema.triggers 
        WHERE event_object_schema = 'public'
        ORDER BY event_object_table, trigger_name;
      `
    });

    if (error) {
      console.log('⚠️ Cannot query triggers. Manual inspection needed.');
      return [];
    }

    console.log(`📋 Found ${data.length} triggers in public schema:`);
    data.forEach(trigger => {
      console.log(`   - ${trigger.trigger_name} on ${trigger.event_object_table} (${trigger.action_timing} ${trigger.event_manipulation})`);
    });

    return data;

  } catch (error) {
    console.error('❌ Error with alternative trigger search:', error);
    return [];
  }
}

async function findConstraintsWithValidateEnum() {
  console.log('🔍 Searching for constraints that use validate_enum_value...');
  
  try {
    const { data, error } = await supabase.rpc('sql', {
      query: `
        SELECT 
          table_name,
          constraint_name,
          constraint_type,
          check_clause
        FROM information_schema.table_constraints tc
        LEFT JOIN information_schema.check_constraints cc 
          ON tc.constraint_name = cc.constraint_name
        WHERE tc.constraint_type = 'CHECK'
          AND (cc.check_clause LIKE '%validate_enum_value%' 
               OR cc.check_clause LIKE '%last_sign_in%');
      `
    });

    if (error) {
      console.log('⚠️ Cannot query constraints directly.');
      return [];
    }

    if (data && data.length > 0) {
      console.log(`📋 Found ${data.length} constraints using validate_enum_value or last_sign_in:`);
      data.forEach(constraint => {
        console.log(`   - ${constraint.constraint_name} on ${constraint.table_name}`);
        console.log(`     Type: ${constraint.constraint_type}`);
        console.log(`     Check: ${constraint.check_clause}`);
      });
      return data;
    } else {
      console.log('✅ No constraints found using validate_enum_value or last_sign_in');
      return [];
    }

  } catch (error) {
    console.error('❌ Error searching constraints:', error);
    return [];
  }
}

async function findFunctionsWithValidateEnum() {
  console.log('🔍 Searching for functions that use validate_enum_value...');
  
  try {
    const { data, error } = await supabase.rpc('sql', {
      query: `
        SELECT 
          proname as function_name,
          prosrc as function_body
        FROM pg_proc 
        WHERE prosrc LIKE '%validate_enum_value%' 
           OR prosrc LIKE '%last_sign_in%'
           OR proname LIKE '%validate%'
           OR proname LIKE '%last_sign%';
      `
    });

    if (error) {
      console.log('⚠️ Cannot query functions directly.');
      return [];
    }

    if (data && data.length > 0) {
      console.log(`📋 Found ${data.length} functions related to validate_enum_value or last_sign_in:`);
      data.forEach(func => {
        console.log(`   - ${func.function_name}`);
        if (func.function_body.includes('validate_enum_value') || func.function_body.includes('last_sign_in')) {
          console.log(`     Contains: validate_enum_value or last_sign_in`);
        }
      });
      return data;
    } else {
      console.log('✅ No functions found using validate_enum_value or last_sign_in');
      return [];
    }

  } catch (error) {
    console.error('❌ Error searching functions:', error);
    return [];
  }
}

async function checkProfilesTableStructure() {
  console.log('🔍 Checking profiles table structure...');
  
  try {
    const { data, error } = await supabase.rpc('sql', {
      query: `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_name = 'profiles' 
          AND table_schema = 'public'
        ORDER BY ordinal_position;
      `
    });

    if (error) {
      console.log('⚠️ Cannot query table structure.');
      return;
    }

    console.log('📋 Profiles table structure:');
    data.forEach(column => {
      console.log(`   - ${column.column_name}: ${column.data_type} ${column.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      if (column.column_default) {
        console.log(`     Default: ${column.column_default}`);
      }
    });

    // Check if last_sign_in field exists
    const hasLastSignIn = data.some(col => col.column_name === 'last_sign_in');
    console.log(`Last sign in field: ${hasLastSignIn ? '✅ EXISTS' : '❌ NOT FOUND'}`);

  } catch (error) {
    console.error('❌ Error checking table structure:', error);
  }
}

async function testValidateEnumFunction() {
  console.log('🧪 Testing validate_enum_value function...');
  
  try {
    // Test with different parameter types
    const tests = [
      { value: 'patient', type: 'user_role', description: 'text, text' },
      { value: null, type: 'user_role', description: 'null, text' }
    ];

    for (const test of tests) {
      try {
        const { data, error } = await supabase.rpc('validate_enum_value', {
          value_to_check: test.value,
          enum_type: test.type
        });

        if (error) {
          console.log(`❌ Test failed (${test.description}):`, error.message);
        } else {
          console.log(`✅ Test passed (${test.description}): ${data}`);
        }
      } catch (err) {
        console.log(`❌ Test exception (${test.description}):`, err.message);
      }
    }

  } catch (error) {
    console.error('❌ Error testing function:', error);
  }
}

async function simulateUserUpdate() {
  console.log('🧪 Simulating user update to trigger error...');
  
  try {
    // Get a test profile
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .limit(1);

    if (profileError || !profiles || profiles.length === 0) {
      console.log('⚠️ No profiles found for testing');
      return;
    }

    const testProfile = profiles[0];
    console.log(`📝 Testing with profile: ${testProfile.email}`);

    // Try to update the profile (this might trigger the error)
    const { data, error } = await supabase
      .from('profiles')
      .update({ 
        updated_at: new Date().toISOString()
      })
      .eq('id', testProfile.id)
      .select();

    if (error) {
      console.log('❌ Profile update triggered error:', error.message);
      if (error.message.includes('validate_enum_value')) {
        console.log('🎯 FOUND THE ISSUE! Profile updates are calling validate_enum_value');
      }
    } else {
      console.log('✅ Profile update successful');
    }

  } catch (error) {
    console.error('❌ Error simulating update:', error);
    if (error.message && error.message.includes('validate_enum_value')) {
      console.log('🎯 FOUND THE ISSUE! Profile operations are calling validate_enum_value');
    }
  }
}

async function main() {
  console.log('🚀 Starting search for problematic triggers and constraints...');
  
  try {
    // 1. Find triggers
    await findTriggersWithValidateEnum();
    
    // 2. Find constraints
    await findConstraintsWithValidateEnum();
    
    // 3. Find functions
    await findFunctionsWithValidateEnum();
    
    // 4. Check table structure
    await checkProfilesTableStructure();
    
    // 5. Test function
    await testValidateEnumFunction();
    
    // 6. Simulate update to trigger error
    await simulateUserUpdate();
    
    console.log('\n📊 Search completed!');
    console.log('💡 If you found triggers or constraints using validate_enum_value,');
    console.log('   those are likely causing the error when users sign in.');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

main().catch(console.error);
