const { createClient } = require('@supabase/supabase-js');
const path = require('path');

require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testLoginAfterFix() {
  console.log('🧪 Testing Login After Function Fix');
  console.log('='.repeat(50));
  
  // Use an existing user or create a simple one
  const testEmail = '<EMAIL>';
  const testPassword = 'SimpleTest123!';
  
  try {
    console.log('🔐 Attempting login with existing user...');
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });
    
    if (error) {
      console.error('❌ Login still failing:', error);
      console.log('📋 Error details:', {
        message: error.message,
        status: error.status,
        code: error.code
      });
      
      // Try creating a new user with minimal data
      console.log('\n🔧 Trying to create new simple user...');
      
      const newEmail = `fix-test-${Date.now()}@example.com`;
      const { data: signupData, error: signupError } = await supabase.auth.signUp({
        email: newEmail,
        password: testPassword
      });
      
      if (signupError) {
        console.error('❌ Signup also failing:', signupError);
        return false;
      }
      
      console.log('✅ Signup successful, trying login...');
      
      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email: newEmail,
        password: testPassword
      });
      
      if (loginError) {
        console.error('❌ Login after signup failed:', loginError);
        return false;
      }
      
      console.log('✅ Login after signup successful!');
      return true;
    }
    
    console.log('✅ Login successful!');
    console.log('👤 User:', data.user?.email);
    
    return true;
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

async function main() {
  const success = await testLoginAfterFix();
  
  if (success) {
    console.log('\n🎉 LOGIN FIX SUCCESSFUL!');
    console.log('✅ You can now login normally');
    console.log('✅ The missing function issue is resolved');
  } else {
    console.log('\n❌ Login still has issues');
    console.log('🔧 Additional steps needed:');
    console.log('1. Check if the function was created properly');
    console.log('2. Look for other triggers causing issues');
    console.log('3. Consider disabling problematic triggers');
  }
}

main().catch(console.error);
