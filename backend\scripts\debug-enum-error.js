#!/usr/bin/env node

/**
 * Debug Enum Validation Error
 * This script tries to reproduce the validate_enum_value error and find its source
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testAuthOperations() {
  console.log('🔐 Testing auth operations that might trigger the error...');
  
  const testEmail = `debug-test-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  
  try {
    // 1. Create user
    console.log('👤 Creating test user...');
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true,
      user_metadata: {
        full_name: 'Debug Test User',
        role: 'patient'
      }
    });

    if (authError) {
      console.error('❌ Auth user creation failed:', authError);
      if (authError.message.includes('validate_enum_value')) {
        console.log('🎯 FOUND IT! User creation triggers validate_enum_value error');
      }
      return;
    }

    console.log('✅ User created successfully');

    // 2. Try to sign in (this might trigger last_sign_in update)
    console.log('🔑 Testing sign in...');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });

    if (signInError) {
      console.error('❌ Sign in failed:', signInError);
      if (signInError.message.includes('validate_enum_value')) {
        console.log('🎯 FOUND IT! Sign in triggers validate_enum_value error');
      }
    } else {
      console.log('✅ Sign in successful');
    }

    // 3. Try to update user metadata
    console.log('📝 Testing user metadata update...');
    const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
      authData.user.id,
      {
        user_metadata: {
          full_name: 'Updated Debug Test User',
          role: 'patient',
          last_updated: new Date().toISOString()
        }
      }
    );

    if (updateError) {
      console.error('❌ User metadata update failed:', updateError);
      if (updateError.message.includes('validate_enum_value')) {
        console.log('🎯 FOUND IT! User metadata update triggers validate_enum_value error');
      }
    } else {
      console.log('✅ User metadata update successful');
    }

    // 4. Try profile operations
    console.log('👤 Testing profile operations...');
    
    // Check if profile exists
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError) {
      console.error('❌ Profile fetch failed:', profileError);
      if (profileError.message.includes('validate_enum_value')) {
        console.log('🎯 FOUND IT! Profile fetch triggers validate_enum_value error');
      }
    } else if (profile) {
      console.log('✅ Profile found');
      
      // Try to update profile
      const { data: profileUpdate, error: profileUpdateError } = await supabase
        .from('profiles')
        .update({ 
          full_name: 'Updated Profile Name',
          updated_at: new Date().toISOString()
        })
        .eq('id', authData.user.id);

      if (profileUpdateError) {
        console.error('❌ Profile update failed:', profileUpdateError);
        if (profileUpdateError.message.includes('validate_enum_value')) {
          console.log('🎯 FOUND IT! Profile update triggers validate_enum_value error');
        }
      } else {
        console.log('✅ Profile update successful');
      }
    } else {
      console.log('⚠️ No profile found, creating one...');
      
      const { data: newProfile, error: newProfileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email: testEmail,
          full_name: 'Debug Test User',
          role: 'patient',
          is_active: true,
          email_verified: true
        });

      if (newProfileError) {
        console.error('❌ Profile creation failed:', newProfileError);
        if (newProfileError.message.includes('validate_enum_value')) {
          console.log('🎯 FOUND IT! Profile creation triggers validate_enum_value error');
        }
      } else {
        console.log('✅ Profile created successfully');
      }
    }

    // 5. Clean up
    console.log('🧹 Cleaning up test data...');
    await supabase.from('profiles').delete().eq('id', authData.user.id);
    await supabase.auth.admin.deleteUser(authData.user.id);
    console.log('✅ Cleanup completed');

  } catch (error) {
    console.error('❌ Test failed with exception:', error);
    if (error.message && error.message.includes('validate_enum_value')) {
      console.log('🎯 FOUND IT! Exception contains validate_enum_value error');
      console.log('Error details:', error.message);
    }
  }
}

async function testDifferentEnumFunctionSignatures() {
  console.log('🧪 Testing different validate_enum_value function signatures...');
  
  const tests = [
    {
      name: 'Standard text, text',
      params: { value_to_check: 'patient', enum_type: 'user_role' }
    },
    {
      name: 'NULL value',
      params: { value_to_check: null, enum_type: 'user_role' }
    },
    {
      name: 'Empty string',
      params: { value_to_check: '', enum_type: 'user_role' }
    },
    {
      name: 'Invalid enum type',
      params: { value_to_check: 'patient', enum_type: 'invalid_type' }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`   Testing: ${test.name}`);
      const { data, error } = await supabase.rpc('validate_enum_value', test.params);
      
      if (error) {
        console.log(`   ❌ ${test.name}: ${error.message}`);
        if (error.message.includes('unknown')) {
          console.log('   🎯 This might be the signature issue!');
        }
      } else {
        console.log(`   ✅ ${test.name}: ${data}`);
      }
    } catch (err) {
      console.log(`   ❌ ${test.name} exception: ${err.message}`);
      if (err.message.includes('unknown')) {
        console.log('   🎯 This might be the signature issue!');
      }
    }
  }
}

async function createMissingFunctionSignatures() {
  console.log('🔧 Creating missing function signatures...');
  
  console.log('\n📋 SQL to run in Supabase Dashboard:');
  console.log('='.repeat(60));
  
  const sql = `
-- Create function that handles unknown type (the one causing the error)
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check unknown, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Convert unknown to text and use main validation
  RETURN validate_enum_value(value_to_check::text, enum_type);
EXCEPTION
  WHEN OTHERS THEN
    -- If conversion fails, return true (permissive)
    RETURN true;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_enum_value(unknown, text) TO authenticated, anon, service_role;

-- Create function that handles anyelement type
CREATE OR REPLACE FUNCTION validate_enum_value(value_to_check anyelement, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Convert anyelement to text and use main validation
  RETURN validate_enum_value(value_to_check::text, enum_type);
EXCEPTION
  WHEN OTHERS THEN
    -- If conversion fails, return true (permissive)
    RETURN true;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_enum_value(anyelement, text) TO authenticated, anon, service_role;

-- Test the functions
SELECT 
  'Testing function signatures...' as test_start,
  validate_enum_value('patient'::text, 'user_role') as test_text,
  validate_enum_value('patient'::unknown, 'user_role') as test_unknown;
`;

  console.log(sql);
  console.log('='.repeat(60));
  console.log('\nThis should fix the "function validate_enum_value(unknown, text) does not exist" error.');
}

async function checkExistingFunctions() {
  console.log('🔍 Checking existing validate_enum_value functions...');
  
  try {
    // Try to call with different types to see which signatures exist
    const signatures = [
      'text, text',
      'unknown, text', 
      'anyelement, text'
    ];

    for (const sig of signatures) {
      try {
        let result;
        if (sig === 'text, text') {
          result = await supabase.rpc('validate_enum_value', {
            value_to_check: 'patient',
            enum_type: 'user_role'
          });
        }
        
        console.log(`✅ Function signature exists: validate_enum_value(${sig})`);
      } catch (err) {
        console.log(`❌ Function signature missing: validate_enum_value(${sig})`);
        if (err.message.includes('does not exist')) {
          console.log(`   This is likely the missing signature causing the error!`);
        }
      }
    }
  } catch (error) {
    console.error('❌ Error checking functions:', error);
  }
}

async function main() {
  console.log('🚀 Starting debug of validate_enum_value error...');
  
  try {
    // 1. Check existing functions
    await checkExistingFunctions();
    
    // 2. Test different function signatures
    await testDifferentEnumFunctionSignatures();
    
    // 3. Test auth operations
    await testAuthOperations();
    
    // 4. Provide solution
    await createMissingFunctionSignatures();
    
    console.log('\n📊 Debug completed!');
    console.log('💡 The error is likely caused by missing function signatures.');
    console.log('   Run the SQL above in your Supabase dashboard to fix it.');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

main().catch(console.error);
